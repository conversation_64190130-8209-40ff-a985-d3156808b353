<?php

function dbMysql($server, $db) {
    // Use the global mysqli connection if available
    global $mysqli_connection;
    if (isset($mysqli_connection) && $mysqli_connection) {
        return $mysqli_connection;
    }

    // Fallback to creating new connection using constants
    if (defined('SERVERDB') && defined('USERDB') && defined('PASSDB') && defined('DB')) {
        $link = mysqli_connect(SERVERDB, USERDB, PASSDB, DB);
    } else {
        // Legacy fallback
        $link = mysqli_connect($server, "entersql", "pass.mobid", $db);
    }

    if (!$link) {
        echo "Error conectando a la base de datos: " . mysqli_connect_error();
        exit();
    }

    return $link;
}

function validacion_quota_sms() {
    $company = $_SESSION["id"];
    $max_msg = $_SESSION["max_msg"];
    if ($max_msg == -1) {
        return true;
    } else {
        // Use prepared statements to prevent SQL injection
        $link = dbMysql(null, null);
        $query = "SELECT cm.max_total_msgs as max, 
                  (SELECT COUNT(*) FROM trafficMT t WHERE t.company = cm.id) as actual 
                  FROM company cm 
                  WHERE cm.id = ?";
        
        $stmt = mysqli_prepare($link, $query);
        mysqli_stmt_bind_param($stmt, "i", $company);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $row = mysqli_fetch_array($result);
        
        if ($row) {
            $max = $row['max'];
            $actual = $row['actual'];
            if ($actual < $max) {
                return true;
            }
        }
        
        mysqli_stmt_close($stmt);
    }
    return false;
}

function obtener_parameter($parameter, $method) {
    $result = '';
    if (strcmp($method, "GET") == 0) {
        if (isset($_GET[$parameter])) {
            $result = filter_input(INPUT_GET, $parameter, FILTER_SANITIZE_STRING);
        }
    } elseif (strcmp($method, "POST") == 0) {
        if (isset($_POST[$parameter])) {
            $result = filter_input(INPUT_POST, $parameter, FILTER_SANITIZE_STRING);
        }
    }
    return $result;
}

function add_process($company, $input, $target, $login) {
    if (empty($target)) {
        $target = "(NULL)";
    }
    if (empty($login)) {
        $login = $_SESSION["user"];
    }

    $link = dbMysql(null, null);
    $query = "INSERT INTO process (company, timestamp, input_mode, target, content, login)
              VALUES (?, UTC_TIMESTAMP(), ?, ?, 'TRANS', ?)";

    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "isss", $company, $input, $target, $login);
    mysqli_stmt_execute($stmt);
    mysqli_stmt_close($stmt);
}

function add_process_file($company, $input, $stage, $target, $login) {
    if (empty($target)) {
        $target = 'NULL';
    }

    $link = dbMysql(null, null);
    $query = "INSERT INTO process (company, timestamp, input_mode, target, content, login)
              VALUES (?, UTC_TIMESTAMP(), ?, ?, ?, ?)";

    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "issss", $company, $input, $target, $stage, $login);
    mysqli_stmt_execute($stmt);
    mysqli_stmt_close($stmt);
}

function del_process($company, $id_process) {
    $link = dbMysql(null, null);
    $query = "DELETE FROM process WHERE company = ? AND id = ?";

    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "ii", $company, $id_process);
    mysqli_stmt_execute($stmt);
    mysqli_stmt_close($stmt);
}

function validate_format_phone($phone, $format) {
    $regex_CL = '/^(\+56|56)([0-9]){9}$/';
    $regex_PE = '/^(\+51|51)([0-9]){9}$/';
    $regex_MX = '/^(\52|521)([0-9]){10}$/';
    $response = false;
    $regex = "";
    
    switch ($format) {
        case "CL":
            $regex = $regex_CL;
            break;
        case "PE":
            $regex = $regex_PE;
            break;
        case "MX":
            $regex = $regex_MX;
            break;
    }
    
    if (!empty($regex)) {
        if (preg_match($regex, $phone, $matchPhone)) {
            $response = true;
        }
    }
    return $response;
}

function validate_format_datetime($fecha) {
    /*
     * expresion regular para validar fecha en formato 
     * 31-12-2017 23:59:59
     */
    $response = false;
    $regexFecha = '/^([0-2][0-9]|3[0-1])(-)(0[1-9]|1[0-2])\2(\d{4})(\s)([0-1][0-9]|2[0-3])(:)([0-5][0-9])(:)([0-5][0-9])$/';
    if (preg_match($regexFecha, $fecha, $matchFecha)) {
        $response = true;
    }
    return $response;
}

function validate_time_available_send_message($fecha) {
    $response = false;
    $company = $_SESSION["id"];
    if (!empty($fecha)) {
        $link = dbMysql(null, null);
        $query = "SELECT fn_common_validate_date_aviable_send(?, ?) as resp";

        $stmt = mysqli_prepare($link, $query);
        mysqli_stmt_bind_param($stmt, "si", $fecha, $company);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $row = mysqli_fetch_array($result);

        if ($row) {
            $resp = $row['resp'];
            if (!empty($resp) && $resp > 0) {
                $response = true;
            }
        }

        mysqli_stmt_close($stmt);
    }
    return $response;
}

function convertirOffset($offset, $inversor) {
    $minutos = 60;
    if ($offset > 0) {
        $offset = $offset * $minutos * $inversor;
        return (string)$offset;
    } elseif ($offset < 0) {
        $offset = $offset * $minutos * $inversor;
        return (string)$offset;
    } else {
        return "0";
    }
}

function insertTempFile($name_file, $date_local_file) {
    $id = 0;
    $company = $_SESSION["id"];
    $login = $_SESSION["user"];

    $link = dbMysql(null, null);
    $query = "INSERT INTO load_file (name_file, company, login, date_local, date_gmt)
              VALUES (?, ?, ?, STR_TO_DATE(?, '%d-%m-%Y %H:%i:%s'), UTC_TIMESTAMP())";

    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "siss", $name_file, $company, $login, $date_local_file);
    mysqli_stmt_execute($stmt);
    $id = mysqli_insert_id($link);
    mysqli_stmt_close($stmt);

    return $id;
}

function updateStatusProcess($id_process, $error_text, $status) {
    $link = dbMysql(null, null);
    $query = "UPDATE process SET error_msg = ?, content = ? WHERE id = ?";

    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "ssi", $error_text, $status, $id_process);
    mysqli_stmt_execute($stmt);
    mysqli_stmt_close($stmt);
}

function updateProcessFile($filename_tmp, $id_process, $count_rows, $error_text, $status) {
    $link = dbMysql(null, null);
    $query = "UPDATE process SET cont_rows = ?, error_msg = ?, target = ?, content = ? WHERE id = ?";

    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "isssi", $count_rows, $error_text, $filename_tmp, $status, $id_process);
    mysqli_stmt_execute($stmt);
    mysqli_stmt_close($stmt);
}

function updateProcessedFile($id_process, $status) {
    $link = dbMysql(null, null);
    $query = "UPDATE process SET content = ? WHERE id = ?";

    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "si", $status, $id_process);
    mysqli_stmt_execute($stmt);
    mysqli_stmt_close($stmt);
}

function insertDetailTableTemporal($data_insert) {
    $link = dbMysql(null, null);
    $query = "INSERT INTO detail_process(
        id_process, number_process, date_local_process,
        date_gmt_process, msg_process, carrier_process, status_detprocess
    ) VALUES $data_insert";

    mysqli_query($link, $query);
    $id = mysqli_insert_id($link);
    return $id;
}

function updateDetailProcessToProcessed($id_process, $status) {
    $result = 0;
    if ($id_process > 0 && !empty($status)) {
        $link = dbMysql(null, null);
        $query = "UPDATE detail_process SET status_detprocess = ? WHERE id_process = ?";

        $stmt = mysqli_prepare($link, $query);
        mysqli_stmt_bind_param($stmt, "si", $status, $id_process);
        mysqli_stmt_execute($stmt);
        $result = mysqli_affected_rows($link);
        mysqli_stmt_close($stmt);
    }
    return $result;
}

function insertarMtDesdeTablaTemporal($id_process, $date_received) {
    $company = $_SESSION["id"];
    $login = $_SESSION["user"];

    $link = dbMysql(null, null);
    $query = "INSERT INTO trafficMT (
        company, recipientId, recipientDomain, status, receivedTime, dispatchTime,
        login, input_process, input_mode, msgText
    ) (
        SELECT ?, d.number_process, d.carrier_process, 'QUEUED', ?,
        STR_TO_DATE(d.date_gmt_process, '%d-%m-%Y %H:%i:%s'), ?, ?, 'UPLOAD', d.msg_process
        FROM detail_process d
        WHERE id_process = ?
    )";

    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "issii", $company, $date_received, $login, $id_process, $id_process);
    mysqli_stmt_execute($stmt);
    $id = mysqli_insert_id($link);
    mysqli_stmt_close($stmt);

    return $id;
}

function add_process_uploadfile($input_mode, $fileName, $count_rows, $date_local) {
    $company = $_SESSION["id"];
    $login = $_SESSION["user"];

    $link = dbMysql(null, null);
    $query = "INSERT INTO process (
        company, timestamp, input_mode, target, content, login, date_gmt, cont_rows
    ) VALUES (?, ?, ?, ?, 'PENDING', ?, UTC_TIMESTAMP(), ?)";

    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "issssi", $company, $date_local, $input_mode, $fileName, $login, $count_rows);
    mysqli_stmt_execute($stmt);
    $id = mysqli_insert_id($link);
    mysqli_stmt_close($stmt);

    return $id;
}

function validate_permisson($codigo) {
    $login = $_SESSION["user"];
    $link = dbMysql(null, null);
    $query = "SELECT COUNT(*) as cant
              FROM account a
              INNER JOIN account_profile ac ON a.id = ac.id_account
              INNER JOIN profile p2 ON ac.id_profile = p2.id_profile
              INNER JOIN profile_permisson pp ON p2.id_profile = pp.id_profile
              INNER JOIN permisson p ON p.id_permisson = pp.id_permisson
              WHERE a.login = ? AND p.cod_permisson = ?";

    $cant = 0;
    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "ss", $login, $codigo);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if ($result) {
        $row = mysqli_fetch_array($result);
        $cant = $row['cant'];
    }

    mysqli_stmt_close($stmt);
    return $cant;
}

function get_count_row_query($query) {
    $textsql = "SELECT COUNT(*) AS cant FROM ($query) AS T";
    $link = dbMysql(null, null);

    $result = mysqli_query($link, $textsql);
    $cant = 0;

    if ($result) {
        $row = mysqli_fetch_array($result);
        $cant = $row['cant'];
    }

    return $cant;
}

function generateRandomString($length = 10) {
    return substr(str_shuffle(str_repeat($x = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length / strlen($x)))), 1, $length);
}

function sendMailPass($pass, $mail) {
    $codeApp = "app1";
    $subject = "Clave De Sistema";
    $to = $mail;

    $body = "<html><body><div>Su nueva clave es: <b>$pass</b></div></body></html>";

    $listMails[0] = $to;
    $listTo = array("listMails" => $listMails);
    $listMails[0] = "";
    $listCc = array("listMails" => $listMails);
    $listMails[0] = "";
    $listCco = array("listMails" => $listMails);

    $mail = array(
        "codApp" => $codeApp,
        "subject" => $subject,
        "toAddress" => $listTo,
        "ccAddress" => $listCc,
        "ccoAddress" => $listCco,
        "messageBody" => $body
    );
    $list = array();
    $list[0] = $mail;
    // $response = callPostRest($request);
    return true;
}

function resetPassword($id) {
    $token = $_SESSION["TOKEN"];
    $url = "";
    if (defined("URL_SERVICE_ACCOUNT")) {
        $url = URL_SERVICE_ACCOUNT;
    }
    $response = "";
    if (strlen($url) > 0) {
        $request = curl_init($url . "/api/user/resetPass/" . $id);
        curl_setopt($request, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($request, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json;charset=UTF-8',
            'Authorization: ' . $token
        ));
        curl_setopt($request, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($request);
        curl_close($request);
    }
    return $response;
}

function obtenerEncoding($ruta) {
    $encoding = "";
    $cmd = 'file -bi ' . $ruta;
    $retorno_cmd = shell_exec($cmd);
    $arr = explode(";", $retorno_cmd);
    $arr = explode("=", $arr[1]);
    $encoding = trim($arr[1]);
    return $encoding;
}

function modificarEncoding($path, $file, $encoding) {
    $source = $path . '/' . $file;
    $target_file = 'utf-8_' . $file;
    $target = $path . '/' . $target_file;
    if ($encoding == "utf-8") {
        $cmd_mv = "mv $source $target";
        shell_exec($cmd_mv);
    } else {
        $cmd_iconv = "iconv -t utf-8 -f $encoding -o $target $source";
        shell_exec($cmd_iconv);
    }
    return $target_file;
}

function obtenerParametros($selMessage) {
    $regex = "/==P[0-9]==/i";
    $coincidencias = "";
    preg_match_all($regex, $selMessage, $coincidencias, PREG_SET_ORDER);
    $arrParametros = [];

    foreach ($coincidencias as $valor) {
        $arrParametros[] = $valor[0];
    }
    return $arrParametros;
}

function quitarEnter($texto) {
    $buscar = array("\r\n", "\n", "\r");
    $reemplazar = array("", "", "");
    $response = str_ireplace($buscar, $reemplazar, $texto);
    return $response;
}

function textoNombreColumnasFileCampaing($arrParametros) {
    $texto = "Móvil";
    for ($i = 0; $i < count($arrParametros); $i++) {
        $texto .= "; $arrParametros[$i]";
    }
    return $texto;
}

function insertCampaign($company, $name, $startDate, $endDate, $file, $reagendar, $rows) {
    $id = 0;
    $login = $_SESSION["user"];

    $link = dbMysql(null, null);
    $query = "INSERT INTO campaign (
        id_account, id_company, description_campaign, date_create_campaign,
        date_start_campaign, date_end_campaign, reagendar, file_campaign,
        status, total_campaign
    ) VALUES (
        (SELECT id FROM account WHERE login = ?), ?, ?, UTC_TIMESTAMP(),
        ?, ?, ?, ?, 'PENDING', ?
    )";

    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "sisssssi", $login, $company, $name, $startDate, $endDate, $reagendar, $file, $rows);
    mysqli_stmt_execute($stmt);
    $id = mysqli_insert_id($link);
    mysqli_stmt_close($stmt);

    return $id;
}

function insertDetailCampaign($id_process, $id_campaign) {
    $status = 'PENDING';

    $link = dbMysql(null, null);
    $query = "INSERT INTO detail_campaign (
        id_campaign, number_detcamp, message_detcamp, status_detcamp
    ) (
        SELECT ?, number_process, msg_process, ?
        FROM detail_process
        WHERE id_process = ?
    )";

    $stmt = mysqli_prepare($link, $query);
    mysqli_stmt_bind_param($stmt, "isi", $id_campaign, $status, $id_process);
    mysqli_stmt_execute($stmt);
    $id = mysqli_insert_id($link);
    mysqli_stmt_close($stmt);

    return $id;
}

function reeplaceParametersMsg($parameterData, $parameterName, $msgtext, $col_fijas) {
    $valor = "";
    for ($i = 0; $i < count($parameterName); $i++) {
        $parameter = $parameterName[$i];
        $valor = $parameterData[$i + $col_fijas];
        $msgtext = str_replace($parameter, $valor, $msgtext);
    }
    return $msgtext;
}

function redirectLogOff() {
    $server = $_SERVER["SERVER_NAME"];
    $root_site = "ENTERPRISE_CL_v417/";
    $url = "$server/$root_site/expire_session.php";
    header('Location: ' . $url);
}
